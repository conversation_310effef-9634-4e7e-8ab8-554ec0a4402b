package main

import (
	"fmt"
	"jackpot/api/elixir/unseal"
	"jackpot/filelog"
	"jackpot/items"
	"jackpot/users"
	"jackpot/env"
	"jackpot/db"
)

func main() {
	// 初始化环境
	env.Init()
	db.Init()
	
	// 初始化items
	logger, err := filelog.NewFileLogger("test.log", 10, 5)
	if err != nil {
		panic(err)
	}
	
	err = items.InitItems(logger)
	if err != nil {
		panic(err)
	}
	
	err = users.InitUsers(logger)
	if err != nil {
		panic(err)
	}
	
	// 创建handler并初始化
	handler := unseal.NewHandler()
	err = handler.Init(logger, nil)
	if err != nil {
		panic(err)
	}
	
	// 测试用例1: mapId=100, 8个仙丹都有数量
	fmt.Println("=== 测试用例1: mapId=100 ===")
	countList1 := []int64{1, 2, 1, 3, 0, 1, 2, 1}
	
	req1 := unseal.Request{
		Oid:    "test001",
		UserId: "user123",
		Map:    100,
		Counts: countList1,
	}
	
	// 验证请求
	err = handler.Validate(req1)
	if err != nil {
		fmt.Printf("验证失败: %v\n", err)
		return
	}
	
	// 处理请求
	jackpot1, response1, err := handler.Handle(req1)
	if err != nil {
		fmt.Printf("处理失败: %v\n", err)
		return
	}
	
	fmt.Printf("奖池余额: %d\n", jackpot1)
	fmt.Printf("返回物品数量: %d\n", len(response1.Data.(*unseal.Response).Items))
	
	for i, item := range response1.Data.(*unseal.Response).Items {
		if item.Value > 0 {
			fmt.Printf("位置%d: %s (价值:%d, 数量:%d, 总数:%d)\n", 
				i, item.Name, item.Value, item.Count, item.Total)
		}
	}
	
	// 测试用例2: mapId=0, 全地图模式
	fmt.Println("\n=== 测试用例2: mapId=0 (全地图) ===")
	countList2 := make([]int64, 24)
	for i := 0; i < 24; i++ {
		countList2[i] = int64(i%3 + 1) // 1,2,3,1,2,3...
	}
	
	req2 := unseal.Request{
		Oid:    "test002",
		UserId: "user456",
		Map:    0,
		Counts: countList2,
	}
	
	// 验证请求
	err = handler.Validate(req2)
	if err != nil {
		fmt.Printf("验证失败: %v\n", err)
		return
	}
	
	// 处理请求
	jackpot2, response2, err := handler.Handle(req2)
	if err != nil {
		fmt.Printf("处理失败: %v\n", err)
		return
	}
	
	fmt.Printf("奖池余额: %d\n", jackpot2)
	fmt.Printf("返回物品数量: %d\n", len(response2.Data.(*unseal.Response).Items))
	
	nonZeroCount := 0
	for i, item := range response2.Data.(*unseal.Response).Items {
		if item.Value > 0 {
			fmt.Printf("位置%d: %s (价值:%d, 数量:%d, 总数:%d)\n", 
				i, item.Name, item.Value, item.Count, item.Total)
			nonZeroCount++
		}
	}
	fmt.Printf("有效物品数量: %d\n", nonZeroCount)
	
	fmt.Println("\n测试完成!")
}
