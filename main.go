package main

// 主程序包，实现一个基于WebSocket的抽奖系统客户端

import (
	"bufio"           // 缓冲IO，用于读取用户输入
	"bytes"           // 字节操作
	"encoding/binary" // 二进制编码/解码
	"encoding/json"   // JSON处理
	"flag"            // 命令行参数解析
	"fmt"             // 格式化IO
	"io"              // 基础IO接口
	"log"             // 日志记录
	"math/rand/v2"    // 随机数生成
	"net/http"        // HTTP客户端
	"os"              // 操作系统功能
	"sort"            // 排序
	"strconv"         // 字符串转换
	"strings"         // 字符串处理
	"sync"            // 同步原语
	"time"            // 时间处理

	"github.com/fasthttp/websocket" // WebSocket客户端库
)

// Item 定义奖品项
// ID - 奖品唯一标识
// Name - 奖品名称
// Value - 奖品价值
// Count - 奖品数量
// Total - 奖品总数(可选字段)
type Item struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	Value int64  `json:"value"`
	Count int    `json:"count"`
	Total int    `json:"total,omitempty"`
}

// Request 定义基础请求结构
// UserId - 用户ID
// Cost - 消耗金额
// Repeat - 重复次数
// Team - 队伍标识
// Jy - 精英标识
// Reset - 重置标识
type Request struct {
	UserId string `json:"uid"`
	Cost   int64  `json:"cost"`
	Repeat int64  `json:"repeat"`
	Team   int64  `json:"team"`
	Jy     int64  `json:"jy"`
	Reset  int64  `json:"reset"`
}

// Request3 定义多人请求结构
// Users - 用户分组映射，key为组别，value为用户ID列表
// Cost - 消耗金额
type Request3 struct {
	Users map[string][]string `json:"users"`
	Cost  int64               `json:"cost"`
}

// Request4 定义模式请求结构
// Mod - 模式类型
// Map - 地图ID(可选)
// Cost - 消耗金额
// Retn - 返回金额
// UserId - 用户ID(可选)
type Request4 struct {
	Mod    int    `json:"mod"`
	Map    int64  `json:"map,omitempty"`
	Cost   int64  `json:"cost"`
	Retn   int64  `json:"retn"`
	UserId string `json:"uid,omitempty"`
}

// Request5 定义游戏局数请求结构
// UserId - 用户ID
// Cost - 消耗金额
// Games - 游戏局数
type Request5 struct {
	UserId string `json:"uid"`
	Cost   int64  `json:"cost"`
	Games  int64  `json:"games"` //游戏局数
}

// Request6 定义地图请求结构
// UserId - 用户ID
// Map - 地图ID
// Cost - 消耗金额
// Multi - 倍数
type Request6 struct {
	UserId string `json:"uid"`
	Map    int64  `json:"map"`
	Cost   int64  `json:"cost"`
	Multi  int64  `json:"multi"`
}

// Request7 定义排行榜请求结构
// RankId - 排行榜ID
type Request7 struct {
	RankId int `json:"id"`
}

// Request9 定义订单请求结构
// OID - 订单ID
// UserId - 用户ID
// Map - 地图ID
// Counts - 奖品数量列表
type Request9 struct {
	OID    string  `json:"oid"`
	UserId string  `json:"uid"`
	Map    int64   `json:"map"`
	Counts []int64 `json:"counts"`
}

type Request10 struct {
	UserId string `json:"uid"`
	Cost   int64  `json:"cost"`
	Repeat int64  `json:"repeat"`
	Jy     int64  `json:"jy"`
}

type Request12 struct {
	OID       string  `json:"oid"`
	UserId    string  `json:"uid"`
	Map       int64   `json:"map"`
	Counts    []int64 `json:"counts"`
	Rejackpot int64   `json:"rejackpot"`
}

// Response 定义基础响应结构
// Code - 响应状态码
// Msg - 响应消息
// Data - 响应数据
//
//	UserId - 用户ID
//	Items - 奖品列表
//	Cost - 消耗金额(可选)
//	Repeat - 重复次数(可选)
//	Count - 数量(可选)
//	Team - 队伍标识(可选)
//	Jy - 精英标识(可选)
type Response struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		UserId string `json:"uid"`
		Items  []Item `json:"items"`
		Cost   int64  `json:"cost,omitempty"`
		Repeat int64  `json:"repeat,omitempty"`
		Count  int64  `json:"count,omitempty"`
		Team   int64  `json:"team,omitempty"`
		Jy     int64  `json:"jy,omitempty"`
	} `json:"data"`
}

type Response3 struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Cost  int64               `json:"cost"`
		Users map[string][]string `json:"users"`
		Items map[string][]Item   `json:"items"`
	} `json:"data"`
}

type Response4 struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		UserId string `json:"uid,omitempty"`
		Cost   int64  `json:"cost"`
		Retn   int64  `json:"retn"`
	} `json:"data"`
}

type Response5 struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		UserId string `json:"uid"`
		Cost   int64  `json:"cost"`
		Games  int64  `json:"games"` //游戏局数
		Items  []Item `json:"items"`
	} `json:"data"`
}

type Respopse6 struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		UserId string `json:"uid"`
		Cost   int64  `json:"cost"`
		Multi  int64  `json:"multi"`
		Items  []Item `json:"items"`
	} `json:"data"`
}

type Response7 struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		RankId int    `json:"id"`
		Items  []Item `json:"items"`
	} `json:"data"`
}

type Response9 struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		OID   string `json:"oid"`
		Map   int    `json:"map"`
		Items []Item `json:"items"`
	} `json:"data"`
}

type Response10 struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Request10
		Items []Item `json:"items"`
	} `json:"data"`
}

// Stats 结构体定义
type Stats struct {
	UserId          string  `json:"uid"`
	TotalCost       int64   `json:"totalCost"`
	TotalReturn     int64   `json:"totalReturn"`
	TotalReturnRate float64 `json:"totalReturnRate"`
}

type MsgChan struct {
	Api    int64
	UserId string
	Cost   int64
	Repeat int64
	Team   int64
	Jy     int64
}

// 全局变量定义
var (
	SERVER_ADDRESS string                    // WebSocket服务器地址
	SINGLE_USER    = false                   // 单用户模式标志
	itemMap        map[string]*Item          // 奖品映射表
	mutex          sync.RWMutex              // 读写锁
	wsc            *websocket.Conn           // WebSocket连接
	wsChan         chan MsgChan              // 消息通道
	wg             sync.WaitGroup            // 等待组
	flagIdx        int64                     // 消息标志索引
	itemCounts     = make(map[int64][]int64) // 奖品数量统计

	// ITEM_MAP 定义奖品名称映射表
	// key为消耗金额，value为对应奖品名称列表
	ITEM_MAP = map[int64][]string{
		100: {
			"摄魂镜",
			"九瓣铜锤",
			"混天绫",
			"火尖枪",
			"羊脂玉净瓶",
			"幌金绳",
			"金铙",
			"人种袋",
		},
		1000: {
			"双股剑",
			"捣药杵",
			"九叶灵芝草",
			"迷魂帕",
			"倒马毒桩",
			"月牙铲",
			"佛宝舍利子",
			"白骨鞭",
		},
		10000: {
			"紫金铃",
			"四明铲",
			"随心铁杆兵",
			"紫金红葫芦",
			"七星剑",
			"乾坤圈",
			"阴阳二气瓶",
			"妖国令旗",
		},
	}
)

// send 处理WebSocket消息发送
// 从wsChan通道读取请求，根据API类型构建不同的消息格式并发送
// 使用switch-case处理不同类型的API请求
func send() {
	for req := range wsChan {
		// 将RequestBody序列化为JSON
		var jsonData []byte
		var err error
		switch req.Api {
		case 0:
			// 构建消息：消息长度 + 消息状态 + 协议类型 + 协议数据（JSON）
			msg := new(bytes.Buffer)
			binary.Write(msg, binary.BigEndian, int32(len(jsonData)+14))
			binary.Write(msg, binary.BigEndian, byte(6))
			binary.Write(msg, binary.BigEndian, byte(0))
			binary.Write(msg, binary.BigEndian, flagIdx)
			if err = wsc.WriteMessage(websocket.BinaryMessage, msg.Bytes()); err != nil {
				panic(err)
			}
			continue
		case 1:
			if jsonData, err = json.Marshal(&Request{
				UserId: req.UserId,
				Cost:   req.Cost,
				Repeat: req.Repeat,
				Team:   req.Team,
				Jy:     req.Jy,
			}); err != nil {
				panic(err)
			}
		case 3:
			var users = make(map[string][]string)
			users["A"] = []string{}
			users["B"] = []string{}
			users["C"] = []string{}
			users["D"] = []string{}
			users["S"] = []string{}
			for i := 0; i < int(req.Repeat); i++ {
				uid := strconv.Itoa(100000 + i)
				var k string
				var r = rand.IntN(4)
				switch r {
				case 0:
					k = "A"
				case 1:
					k = "B"
				case 2:
					k = "C"
				case 3:
					k = "D"
				default:
					k = "D"
				}
				if req.Cost == 100 {
					if len(users[k]) < 100 {
						users[k] = append(users[k], uid)
					}
				} else if req.Cost == 1000 {
					if len(users[k]) < 25 {
						users[k] = append(users[k], uid)
					}
				} else {
					if len(users[k]) < 5 {
						users[k] = append(users[k], uid)
					}
				}
				if req.Team == 1 {
					if len(users["S"]) < 1 {
						users["S"] = append(users["S"], uid)
					}
				}
			}
			if jsonData, err = json.Marshal(&Request3{
				Users: users,
				Cost:  req.Cost,
			}); err != nil {
				panic(err)
			}
		case 4:
			if req.Team == 1 {
				if jsonData, err = json.Marshal(&Request4{
					Mod:    1,
					UserId: req.UserId,
					Cost:   req.Cost,
					Retn:   req.Repeat,
				}); err != nil {
					panic(err)
				}
			} else {
				if jsonData, err = json.Marshal(&Request4{
					Cost: req.Cost,
					Retn: req.Repeat,
				}); err != nil {
					panic(err)
				}
			}
		case 5:
			if jsonData, err = json.Marshal(&Request5{
				UserId: req.UserId,
				Cost:   req.Cost,
				Games:  req.Repeat,
			}); err != nil {
				panic(err)
			}
		case 6:
			if jsonData, err = json.Marshal(&Request6{
				UserId: req.UserId,
				Map:    req.Cost,
				Cost:   req.Cost * req.Repeat,
				Multi:  req.Team,
			}); err != nil {
				panic(err)
			}
		case 7:
			if jsonData, err = json.Marshal(&Request7{
				RankId: int(req.Team),
			}); err != nil {
				panic(err)
			}
		case 8:
			if jsonData, err = json.Marshal(&Request7{
				RankId: int(req.Team),
			}); err != nil {
				panic(err)
			}
		case 9:
			var uid string
			var counts []int64
			if req.Team == 1 {
				var l int
				if req.Cost == 0 {
					l = 24
				} else {
					l = 8
				}
				counts = make([]int64, l)
				total := req.Repeat
				for i := range l - 1 {
					counts[i] = rand.Int64N(total + 1)
					total -= counts[i]
				}
				counts[l-1] = total
				uid = req.UserId
			} else {
				if req.Cost == 0 {
					// map为0时，counts长度为24
					counts = make([]int64, 24)
					// 假设ITEM_MAP中的三个cost分别为100, 1000, 10000
					costs := []int64{100, 1000, 10000}
					offset := 0
					for _, cost := range costs {
						if costCounts, ok := itemCounts[cost]; ok {
							copy(counts[offset:], costCounts)
							offset += len(costCounts)
						}
					}
				} else {
					counts = itemCounts[req.Cost]
				}
				uid = "-1"
			}

			// 生成随机的 5 位整数作为 OID
			oid := rand.IntN(90000) + 10000

			// 发送请求
			if jsonData, err = json.Marshal(&Request9{
				Map:    req.Cost,
				Counts: counts,
				OID:    strconv.Itoa(oid), // 将 oid 转换为字符串类型添加 OID 字段
				UserId: uid,
			}); err != nil {
				panic(err)
			}
		case 10, 11:
			if jsonData, err = json.Marshal(&Request10{
				UserId: req.UserId,
				Cost:   req.Cost,
				Repeat: req.Repeat,
				Jy:     req.Jy,
			}); err != nil {
				panic(err)
			}
		case 12:
			counts := make([]int64, 8)
			l := len(counts)
			total := req.Repeat
			for i := range l - 1 {
				counts[i] = rand.Int64N(total + 1)
				total -= counts[i]
			}
			counts[l-1] = total

			// 生成随机的 5 位整数作为 OID
			oid := rand.IntN(90000) + 10000

			rejackpot := req.Repeat * req.Cost

			// 发送请求
			if jsonData, err = json.Marshal(&Request12{
				Map:       req.Cost,
				Counts:    counts,
				OID:       strconv.Itoa(oid), // 将 oid 转换为字符串类型添加 OID 字段
				UserId:    req.UserId,
				Rejackpot: int64(rejackpot),
			}); err != nil {
				panic(err)
			}
		default:
			log.Fatalf("no api: %d", req.Api)
			continue
		}

		// 构建消息：消息长度 + 消息状态 + 协议类型 + 协议数据（JSON）
		msg := new(bytes.Buffer)
		binary.Write(msg, binary.BigEndian, int32(len(jsonData)+14))
		binary.Write(msg, binary.BigEndian, byte(0))
		binary.Write(msg, binary.BigEndian, byte(req.Api))
		binary.Write(msg, binary.BigEndian, int64(flagIdx))
		binary.Write(msg, binary.BigEndian, jsonData)

		if err := wsc.WriteMessage(websocket.BinaryMessage, msg.Bytes()); err != nil {
			panic(err)
		}

		// 增加 flagIdx 的计数
		flagIdx++
	}
}

func recv() {
	for {
		// 接收消息
		_, message, err := wsc.ReadMessage()
		if err != nil {
			log.Println(err)
		}
		// 解析消息体
		msgLen := binary.BigEndian.Uint32(message[:4])
		if msgLen > uint32(len(message)) {
			log.Printf("receive Error: %d", msgLen)
			wg.Done()
			continue
		}
		msgState := message[4]
		if msgState != 0 && msgState != 5 {
			log.Printf("receive Error: %d", msgState)
			wg.Done()
			continue
		}

		msgType := message[5]
		// msgFlag := message[6:14]
		msgBody := message[14:]
		switch msgType {
		case 1:
			response(msgBody)
		case 3:
			response3(msgBody)
		case 4:
			response4(msgBody)
		case 5:
			response(msgBody)
		case 6:
			response(msgBody)
		case 7:
			response7(msgBody)
		case 8:
			response4(msgBody)
		case 9:
			response9(msgBody)
		case 10:
			response10(msgBody)
		case 11:
			response(msgBody)
		case 12:
			response9(msgBody)
		default:
			log.Println("Unknown msgType:", msgType)
		}

		wg.Done()
	}
}

func response(data []byte) {
	dataStr := string(data)
	log.Printf("%s", dataStr)

	// 解析JSON响应
	var resp Response
	err := json.Unmarshal(data, &resp) // 使用Unmarshal直接解析
	if err != nil {
		log.Fatalf("Error parsing JSON response: %v", err)
	}

	// 根据Response的Code做判断
	if resp.Code == 0 { // 假设0代表成功
		mutex.Lock()
		defer mutex.Unlock()

		// 获取或初始化统计数据
		var data = resp.Data

		var giftMap = ITEM_MAP[data.Cost]
		counts, ok := itemCounts[data.Cost]
		if !ok {
			counts = []int64{0, 0, 0, 0, 0, 0, 0, 0}
		}

		for _, item := range data.Items {
			if _, exists := itemMap[item.Name]; !exists {
				itemMap[item.Name] = &item
			} else {
				itemMap[item.Name].Total += item.Total
			}

			for i, v := range giftMap {
				if item.Name == v {
					counts[i] += int64(item.Total)
				}
			}
		}

		itemCounts[data.Cost] = counts
	}
}

func response3(compressedData []byte) {
	// // 创建 gzip 读取器
	// reader, err := gzip.NewReader(bytes.NewReader(compressedData))
	// if err != nil {
	// 	log.Fatal(err)
	// }
	// defer reader.Close()

	// 创建一个缓冲区来存储解压缩的数据
	var buf bytes.Buffer
	// if _, err := buf.ReadFrom(reader); err != nil {
	// 	log.Fatal(err)
	// }
	if _, err := buf.Write(compressedData); err != nil {
		log.Fatal(err)
	}

	// 将 []byte 数据转换回字符串
	dataStr := buf.String()
	log.Printf("Request content: %s", dataStr)

	// 解析JSON响应
	var resp Response3
	if err := json.Unmarshal(buf.Bytes(), &resp); err != nil { // 使用Unmarshal直接解析
		log.Fatalf("Error parsing JSON response: %v", err)
	}

	// 根据Response的Code做判断
	if resp.Code == 0 { // 假设0代表成功
		mutex.Lock()
		defer mutex.Unlock()

		// 获取或初始化统计数据
		var data = resp.Data

		var totalCost, totalReturn int
		for k, list := range data.Items {
			itemMap = make(map[string]*Item)
			for _, item := range list {
				if _, exists := itemMap[item.Name]; !exists {
					itemMap[item.Name] = &item
				} else {
					itemMap[item.Name].Total += 1
				}
			}
			totalValue := 0
			for _, item := range itemMap {
				totalValue += int(item.Value) * item.Total
				log.Printf("K: %s, Item: %s, Count: %d, Value: %d, Total: %d", k, item.Name, item.Total, item.Value, item.Total*int(item.Value))
			}
			usersCost := len(data.Users[k]) * int(data.Cost)
			totalCost += usersCost
			totalReturn += totalValue
			log.Printf("K: %s, Users: %d, Total: %d, RTP: %.2f%%", k, len(data.Users[k]), totalValue, float64(totalValue)/float64(usersCost)*100)
		}
		log.Printf("Boss Result -- Total Cost: %d, Total Return: %d, RTP: %.2f%%", totalCost, totalReturn, float64(totalReturn)/float64(totalCost)*100)
	}
}

func response4(data []byte) {
	dataStr := string(data)
	log.Printf("Request content: %s", dataStr)
}

func response7(data []byte) {
	dataStr := string(data)
	log.Printf("Request content: %s", dataStr)

	// 解析JSON响应
	var resp Response7
	err := json.Unmarshal(data, &resp) // 使用Unmarshal直接解析
	if err != nil {
		log.Fatalf("Error parsing JSON response: %v", err)
	}

	// 根据Response的Code做判断
	if resp.Code == 0 { // 假设0代表成功
		mutex.Lock()
		defer mutex.Unlock()

		// 获取或初始化统计数据
		var data = resp.Data

		itemMap = make(map[string]*Item)
		for _, item := range data.Items {
			if _, exists := itemMap[item.Name]; !exists {
				itemMap[item.Name] = &item
			} else {
				itemMap[item.Name].Total += item.Total
			}
		}
	}
}

func response9(data []byte) {
	dataStr := string(data)
	log.Printf("%s", dataStr)

	// 解析JSON响应
	var resp Response9
	err := json.Unmarshal(data, &resp) // 使用Unmarshal直接解析
	if err != nil {
		log.Fatalf("Error parsing JSON response: %v", err)
	}

	// 根据Response的Code做判断
	if resp.Code == 0 { // 假设0代表成功
		mutex.Lock()
		defer mutex.Unlock()

		// 获取或初始化统计数据
		var data = resp.Data

		var totalCost, totalReturn int64

		for i, item := range data.Items {
			name := fmt.Sprintf("[%02d]%s", i, item.Name)
			if _, exists := itemMap[name]; !exists {
				itemMap[name] = &item
			} else {
				itemMap[name].Total += item.Total
			}

			var currentMap int64
			if data.Map == 0 {
				if i < 8 {
					currentMap = 100
				} else if i < 16 {
					currentMap = 1000
				} else {
					currentMap = 10000
				}
			} else {
				// 转换 data.Map 类型为 int64
				currentMap = int64(data.Map)
			}

			totalCost += int64(item.Total) * currentMap
			totalReturn += int64(item.Total) * item.Value * int64(item.Count)
		}

		itemCounts[100] = []int64{0, 0, 0, 0, 0, 0, 0, 0}
		itemCounts[1000] = []int64{0, 0, 0, 0, 0, 0, 0, 0}
		itemCounts[10000] = []int64{0, 0, 0, 0, 0, 0, 0, 0}

		log.Printf("OID: %s, Map: %d, Total Cost: %d, Total Return: %d, RTP: %.2f%%", data.OID, data.Map, totalCost, totalReturn, float64(totalReturn)/float64(totalCost)*100)
	}
}

func response10(data []byte) {
	dataStr := string(data)
	log.Printf("%s", dataStr)

	// 解析JSON响应
	var resp Response10
	err := json.Unmarshal(data, &resp) // 使用Unmarshal直接解析
	if err != nil {
		log.Fatalf("Error parsing JSON response: %v", err)
	}

	// 根据Response的Code做判断
	if resp.Code == 0 { // 假设0代表成功
		mutex.Lock()
		defer mutex.Unlock()

		// 获取或初始化统计数据
		var data = resp.Data

		itemMap = make(map[string]*Item)
		for _, item := range data.Items {
			if _, exists := itemMap[item.Name]; !exists {
				itemMap[item.Name] = &item
			} else {
				itemMap[item.Name].Total += item.Total
			}
		}
	}
}

func init() {
	// 定义命令行参数
	flag.StringVar(&SERVER_ADDRESS, "s", "127.0.0.1:30001", "server address")
	// flag.StringVar(&SERVER_ADDRESS, "s", "**********:30001", "server address")
	// flag.StringVar(&SERVER_ADDRESS, "s", "**************:10333", "server address")
}

func main() {
	flag.Parse()

	// 连接到WebSocket服务器
	c, _, err := websocket.DefaultDialer.Dial(fmt.Sprintf("ws://%s/ws/991?v=1.0&timeout=1", SERVER_ADDRESS), nil)
	if err != nil {
		panic(err)
	}
	defer c.Close()

	wsc = c
	wsChan = make(chan MsgChan, 10000)

	go send()
	go recv()

	reader := bufio.NewReader(os.Stdin)

	for {
		fmt.Print("选择模式 (1: 管理员模式, 2: 游戏接口模式): ")
		modeInput, _ := reader.ReadString('\n')
		modeInput = strings.TrimSpace(modeInput)

		if modeInput == "1" {
			fmt.Println("进入管理员模式 (输入 'quit' 返回选择模式)")
			for {
				fmt.Print("输入指令: ")
				adminInput, _ := reader.ReadString('\n')
				adminInput = strings.TrimSpace(adminInput)
				if adminInput == "quit" {
					break
				}

				if strings.HasPrefix(adminInput, "/stats/global") {
					parts := strings.Split(adminInput, " ")
					if len(parts) == 1 {
						// 处理 GET 请求
						resp, err := http.Get(fmt.Sprintf("http://%s%s", SERVER_ADDRESS, parts[0]))
						if err != nil {
							log.Printf("Request error: %v", err)
							continue
						}
						defer resp.Body.Close()

						if resp.StatusCode != http.StatusOK {
							log.Printf("Received non-200 response: %d", resp.StatusCode)
							continue
						}

						bodyBytes, err := io.ReadAll(resp.Body)
						if err != nil {
							log.Printf("Error reading response body: %v", err)
							continue
						}

						bodyString := string(bodyBytes)
						log.Printf("Response: %s\n", bodyString)
					} else if len(parts) == 3 {
						// 处理 POST 请求
						key := parts[1]
						value := parts[2]

						url := fmt.Sprintf("http://%s%s/%s", SERVER_ADDRESS, parts[0], key)
						data := map[string]string{"value": value}
						jsonData, err := json.Marshal(data)
						if err != nil {
							log.Printf("Error marshaling data: %v", err)
							continue
						}

						resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
						if err != nil {
							log.Printf("Request error: %v", err)
							continue
						}
						defer resp.Body.Close()

						if resp.StatusCode != http.StatusOK {
							log.Printf("Received non-200 response: %d", resp.StatusCode)
							continue
						}

						bodyBytes, err := io.ReadAll(resp.Body)
						if err != nil {
							log.Printf("Error reading response body: %v", err)
							continue
						}

						bodyString := string(bodyBytes)
						log.Printf("Response: %s\n", bodyString)
					} else {
						log.Println("Invalid input format. Usage: /stats/global [key rtp]")
					}
				} else if strings.HasPrefix(adminInput, "/stats/cleanup") {
					parts := strings.Split(adminInput, " ")
					url := fmt.Sprintf("http://%s%s", SERVER_ADDRESS, parts[0])
					resp, err := http.Post(url, "application/json", nil)
					if err != nil {
						log.Printf("Request error: %v", err)
						continue
					}
					defer resp.Body.Close()

					if resp.StatusCode != http.StatusOK {
						log.Printf("Received non-200 response: %d", resp.StatusCode)
						continue
					}

					bodyBytes, err := io.ReadAll(resp.Body)
					if err != nil {
						log.Printf("Error reading response body: %v", err)
						continue
					}

					bodyString := string(bodyBytes)
					log.Printf("Response: %s\n", bodyString)
				} else if strings.HasPrefix(adminInput, "/api/modules") {
					// 处理模块信息API请求
					parts := strings.Split(adminInput, " ")
					baseUrl := fmt.Sprintf("http://%s%s", SERVER_ADDRESS, parts[0])

					if len(parts) == 1 {
						// 基础请求：/api/modules
						resp, err := http.Get(baseUrl)
						if err != nil {
							log.Printf("Request error: %v", err)
							continue
						}
						defer resp.Body.Close()

						if resp.StatusCode != http.StatusOK {
							log.Printf("Received non-200 response: %d", resp.StatusCode)
							continue
						}

						bodyBytes, err := io.ReadAll(resp.Body)
						if err != nil {
							log.Printf("Error reading response body: %v", err)
							continue
						}

						bodyString := string(bodyBytes)
						log.Printf("Basic modules response: %s\n", bodyString)
					} else if len(parts) == 2 && parts[1] == "admin" {
						// 管理员模式请求：/api/modules admin
						url := fmt.Sprintf("%s?admin=true", baseUrl)
						resp, err := http.Get(url)
						if err != nil {
							log.Printf("Request error: %v", err)
							continue
						}
						defer resp.Body.Close()

						if resp.StatusCode != http.StatusOK {
							log.Printf("Received non-200 response: %d", resp.StatusCode)
							continue
						}

						bodyBytes, err := io.ReadAll(resp.Body)
						if err != nil {
							log.Printf("Error reading response body: %v", err)
							continue
						}

						bodyString := string(bodyBytes)
						log.Printf("Admin modules response: %s\n", bodyString)
					} else if len(parts) == 3 && parts[1] == "admin" && parts[2] == "detailed" {
						// 管理员详细模式请求：/api/modules admin detailed
						url := fmt.Sprintf("%s?admin=true&detailed=true", baseUrl)
						resp, err := http.Get(url)
						if err != nil {
							log.Printf("Request error: %v", err)
							continue
						}
						defer resp.Body.Close()

						if resp.StatusCode != http.StatusOK {
							log.Printf("Received non-200 response: %d", resp.StatusCode)
							continue
						}

						bodyBytes, err := io.ReadAll(resp.Body)
						if err != nil {
							log.Printf("Error reading response body: %v", err)
							continue
						}

						bodyString := string(bodyBytes)
						log.Printf("Admin detailed modules response: %s\n", bodyString)
					} else {
						log.Println("Invalid input format. Usage: /api/modules [admin] [detailed]")
					}
				} else {
					log.Println("无效的指令，请重新输入")
					log.Println("可用指令:")
					log.Println("  /stats/global/{map} - 获取全局统计")
					log.Println("  /stats/global/{map} [key] [value] - 设置全局统计")
					log.Println("  /stats/cleanup - 清理统计数据")
					log.Println("  /api/modules - 获取基础模块信息")
					log.Println("  /api/modules admin - 获取管理员模式模块信息")
					log.Println("  /api/modules admin detailed - 获取管理员详细模式模块信息")
				}
			}
		} else if modeInput == "2" {
			fmt.Println("进入游戏接口模式 (输入 'quit' 返回选择模式)")

			// 无限循环，等待用户输入并进行请求
			for {
				// 从命令行读取请求次数和成本
				fmt.Print("用户数、请求数、接口、成本、连击、队伍、精英: ")
				input, _ := reader.ReadString('\n')
				input = strings.TrimSpace(input)

				if input == "quit" {
					break
				}

				// 分割输入值
				parts := strings.Split(input, " ")
				if len(parts) != 7 {
					log.Printf("Invalid input format. Please enter 7 parameters.")
					continue // 继续等待新的指令
				}

				usersStr, countStr, apiStr, costStr, repeatStr, teamStr, jyStr := parts[0], parts[1], parts[2], parts[3], parts[4], parts[5], parts[6]
				users, err := strconv.ParseInt(usersStr, 10, 64)
				if err != nil {
					log.Printf("Invalid users: %v", err)
					continue // 继续等待新的指令
				}
				count, err := strconv.ParseInt(countStr, 10, 64)
				if err != nil {
					log.Printf("Invalid count: %v", err)
					continue // 继续等待新的指令
				}
				api, err := strconv.ParseInt(apiStr, 10, 64)
				if err != nil {
					log.Printf("Invalid api: %v", err)
					continue // 继续等待新的指令
				}
				cost, err := strconv.ParseInt(costStr, 10, 64)
				if err != nil {
					log.Printf("Invalid cost: %v", err)
					continue // 继续等待新的指令
				}
				repeat, err := strconv.ParseInt(repeatStr, 10, 64)
				if err != nil {
					log.Printf("Invalid repeat: %v", err)
					continue // 继续等待新的指令
				}
				team, err := strconv.ParseInt(teamStr, 10, 64)
				if err != nil {
					log.Printf("Invalid team: %v", err)
					continue // 继续等待新的指令
				}
				jy, err := strconv.ParseInt(jyStr, 10, 64)
				if err != nil {
					log.Printf("Invalid jy: %v", err)
					continue // 继续等待新的指令
				}

				if users > 1 {
					SINGLE_USER = false
				} else {
					SINGLE_USER = true
				}

				itemMap = make(map[string]*Item)
				start := time.Now() // 记录开始时间

				// 发送多次请求
				var usersList []string
				for i := range int(users) {
					uid := strconv.Itoa(100000 + i)
					for range int(count) {
						usersList = append(usersList, uid)
					}
				}

				// 使用 rand.Shuffle 进行随机排序
				rand.Shuffle(len(usersList), func(i, j int) {
					usersList[i], usersList[j] = usersList[j], usersList[i]
				})

				for _, uid := range usersList {
					wg.Add(1)
					go func() {
						msg := MsgChan{
							Api:    api,
							UserId: uid,
							Cost:   cost,
							Repeat: repeat,
							Team:   team,
							Jy:     jy,
						}
						wsChan <- msg
					}()
				}

				// 等待所有请求完成
				wg.Wait()

				elapsed := time.Since(start) // 计算耗时
				log.Printf("Request took %v", elapsed)

				// 获取所有键并排序
				keys := make([]string, 0, len(itemMap))
				for k := range itemMap {
					keys = append(keys, k)
				}
				sort.Strings(keys)

				// 按排序后的键顺序输出
				for _, key := range keys {
					item := itemMap[key]
					// 计算字符串的实际显示宽度
					width := 0
					for _, r := range key {
						if r >= 0x4E00 && r <= 0x9FFF { // 判断是否为中文字符
							width += 2
						} else {
							width += 1
						}
					}
					// 动态调整格式，确保对齐
					format := fmt.Sprintf("Item: %%-%ds Value: %%-10d Total: %%-10d All: %%-10d", 20+(width-len(key)))
					log.Printf(format, key, item.Value*int64(item.Count), item.Total, item.Total*int(item.Value)*item.Count)
				}
			}
		} else if modeInput == "quit" {
			log.Println("收到退出指令，正在优雅退出程序...")
			close(wsChan) // 关闭消息通道
			if wsc != nil {
				wsc.Close() // 关闭WebSocket连接
			}
			break // 退出main函数
		}
	}

	// 程序退出前的清理工作（如果有的话）
	log.Println("Program exited.")
}
