package main

import (
	"bytes"
	"encoding/binary"
	"flag"
	"fmt"
	"jackpot/api"
	"jackpot/db"
	"jackpot/env"
	"jackpot/filelog"
	"jackpot/items"
	"jackpot/net"
	"jackpot/users"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"runtime/debug"
	"strconv"
	"sync"
	"time"

	"github.com/gofiber/contrib/websocket"
	"github.com/gofiber/fiber/v2"
	"gopkg.in/ini.v1"
)

var (
	logger     *filelog.FileLogger
	flagMap    sync.Map
	apiManager *api.Manager
	startTime  time.Time
)

func init() {
	// 定义命令行参数
	flag.BoolVar(&env.Debug, "d", false, "debug mode")
}

func encodeMsg(msgState byte, msgType byte, msgFlag int64) []byte {
	msg := new(bytes.Buffer)
	binary.Write(msg, binary.BigEndian, int32(14))
	binary.Write(msg, binary.BigEndian, msgState)
	binary.Write(msg, binary.BigEndian, msgType)
	binary.Write(msg, binary.BigEndian, msgFlag)
	var body = make(map[string]any, 0)
	body["state"] = msgState
	body["type"] = msgType
	body["flag"] = msgFlag

	if msgState != net.MSG_STATE_ERROR_HEART {
		logger.Log(filelog.WARN, "ws", "", body)
	}
	return msg.Bytes()
}

func encodeMsgWithData(msgState byte, msgType byte, msgFlag int64, msgJackpot int64, msgData []byte) []byte {
	msg := new(bytes.Buffer)
	binary.Write(msg, binary.BigEndian, int32(len(msgData)+14))
	binary.Write(msg, binary.BigEndian, msgState)
	binary.Write(msg, binary.BigEndian, msgType)
	binary.Write(msg, binary.BigEndian, msgFlag)
	binary.Write(msg, binary.BigEndian, msgData)
	var body = make(map[string]any, 0)
	body["state"] = msgState
	body["type"] = msgType
	body["flag"] = msgFlag
	body["jackpot"] = msgJackpot

	if msgState == net.MSG_STATE_OK {
		logger.Log(filelog.INFO, "ws", string(msgData), body)
	} else {
		logger.Log(filelog.WARN, "ws", string(msgData), body)
	}
	return msg.Bytes()
}

func initLogger(cfg *ini.File) error {
	dir := cfg.Section("log").Key("dir").String()
	pattern := cfg.Section("log").Key("pattern").String()
	lv := cfg.Section("log").Key("lv").String()
	nextTime, err := cfg.Section("log").Key("next_time").Int()
	if err != nil {
		return err
	}
	nextSize, err := cfg.Section("log").Key("next_size").Int()
	if err != nil {
		return err
	}

	logger, err = filelog.NewFileLogger(dir, pattern, lv, nextTime, nextSize)
	if err != nil {
		return err
	}
	return nil
}

func startTimer(sec int) {
	ticker := time.NewTicker(time.Duration(sec) * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		flagMap.Clear()
		logger.Warn("timer", "clear flag map")
	}
}

func main() {
	// 记录服务启动时间
	startTime = time.Now()

	// 解析命令行参数
	flag.Parse()

	env.Arch = runtime.GOARCH

	// 创建一个临时文件用于存储崩溃报告
	timestamp := time.Now().Format("200601")
	fileName := filepath.Join("./", fmt.Sprintf("%s.crash", timestamp))
	f, err := os.OpenFile(fileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		panic(err)
	}
	// 设置崩溃输出
	if err = debug.SetCrashOutput(f, debug.CrashOptions{}); err != nil {
		panic(err)
	}

	cfg, err := ini.Load("config.ini")
	if err != nil {
		panic(err)
	}

	env.PlatId = cfg.Section("base").Key("platid").String()
	env.Version = cfg.Section("base").Key("appver").String()

	// 初始化数据库
	addr := cfg.Section("redis").Key("addr").String()
	pass := cfg.Section("redis").Key("password").String()
	dbname, err := cfg.Section("redis").Key("db").Int()
	if err != nil {
		panic(err)
	}
	if err = db.Run(addr, pass, dbname); err != nil {
		panic(err)
	}

	if err = initLogger(cfg); err != nil {
		panic(err)
	}

	if err = users.InitUsers(logger, cfg); err != nil {
		panic(err)
	}

	if err = items.InitItems(logger); err != nil {
		panic(err)
	}

	// 初始化API管理器
	apiManager = api.NewManager()
	if err = apiManager.Initialize(logger, cfg); err != nil {
		panic(err)
	}

	// 所有模块现在通过API管理器统一管理

	flagMap = sync.Map{}
	// 开启定时清理标记
	sec, err := cfg.Section("base").Key("timer").Int()
	if err != nil {
		panic(err)
	}
	go startTimer(sec)

	app := fiber.New()
	app.Use("/ws", func(c *fiber.Ctx) error {
		if websocket.IsWebSocketUpgrade(c) {
			c.Locals("allowed", true)
			logger.Warn("ws", "client 1 %v", c.IP())
			return c.Next()
		}
		return fiber.ErrUpgradeRequired
	})

	app.Get("/ws/:id", websocket.New(func(c *websocket.Conn) {
		// c.Locals is added to the *websocket.Conn
		// 打印客户端信息
		body := make(map[string]any)
		body["allowed"] = c.Locals("allowed")
		body["platid"] = c.Params("id")
		body["version"] = c.Query("v")
		body["timeout"] = c.Query("timeout")
		body["session"] = c.Cookies("session")
		body["address"] = c.RemoteAddr().String()
		logger.Log(filelog.WARN, "ws", "client 2", body)

		timeoutStr := c.Query("timeout")
		var timeout time.Duration
		if len(timeoutStr) > 0 {
			if t, err := strconv.Atoi(timeoutStr); err != nil {
				logger.Warn("ws", "timeout %s atoi error: %v", timeoutStr, err)
				return
			} else {
				timeout = time.Duration(t) * time.Second
			}
		}

		var (
			mt  int
			msg []byte
			err error
		)
		for {
			if mt, msg, err = c.ReadMessage(); err != nil {
				logger.Error("ws", "read mt:%d err:%v", mt, err)
				break
			}

			if mt != websocket.BinaryMessage {
				logger.Warn("ws", "not binaryMessage err:%v", err)
				continue
			}

			const msgHeadSize = 14

			reader := bytes.NewReader(msg)
			for reader.Len() >= msgHeadSize {
				// 使用 binary.Read 来更安全地解析消息头部信息
				var msgLen int32
				var msgState byte
				var msgType byte
				var msgFlag int64

				err = binary.Read(reader, binary.BigEndian, &msgLen)
				if err != nil {
					logger.Warn("ws", "binary read msgLen err: %v", err)
					break
				}
				err = binary.Read(reader, binary.BigEndian, &msgState)
				if err != nil {
					logger.Warn("ws", "binary read msgState err: %v", err)
					break
				}
				err = binary.Read(reader, binary.BigEndian, &msgType)
				if err != nil {
					logger.Warn("ws", "binary read msgType err: %v", err)
					break
				}
				err = binary.Read(reader, binary.BigEndian, &msgFlag)
				if err != nil {
					logger.Warn("ws", "binary read msgFlag err: %v", err)
					break
				}

				if msgState == net.MSG_STATE_ERROR_HEART {
					var send = encodeMsg(net.MSG_STATE_ERROR_HEART, msgType, msgFlag)
					if err = c.WriteMessage(mt, send); err != nil {
						logger.Error("ws", "write err: %v", err)
					}
					continue
				}

				if int(msgLen) <= msgHeadSize {
					continue
				}

				// 读取消息体
				msgBody := make([]byte, msgLen-msgHeadSize)
				err = binary.Read(reader, binary.BigEndian, &msgBody)
				if err != nil {
					logger.Warn("ws", "binary read msgBody err: %v", err)
					continue
				}

				if body, ok := flagMap.Load(msgFlag); ok {
					logger.Warn("ws", "msg flag repeat: %d", msgFlag)
					var send = encodeMsgWithData(net.MSG_STATE_ERROR_DUPLICATE, msgType, msgFlag, 0, body.([]byte))
					if err := c.WriteMessage(mt, send); err != nil {
						logger.Error("ws", "write err: %v", err)
					}
					continue
				}

				// 使用API管理器处理消息
				done := make(chan net.RequestChannel)

				if !apiManager.ProcessMessage(msgType, msgBody, done) {
					logger.Warn("ws", "no found msg: %d", msgType)
					var send = encodeMsg(net.MSG_STATE_ERROR_UNDIFINE, msgType, msgFlag)
					if err := c.WriteMessage(mt, send); err != nil {
						logger.Error("ws", "write err: %v", err)
					}
					continue
				}

				if timeout > 0 {
					// 创建一个计时器
					timer := time.NewTimer(timeout)

					select {
					case <-timer.C:
						logger.Warn("ws", "Message processing timeout: type(%d) flag(%d)", msgType, msgFlag)
						timer.Stop()
						var send = encodeMsg(net.MSG_STATE_TIMEOUT, msgType, msgFlag)
						if err := c.WriteMessage(mt, send); err != nil {
							logger.Error("ws", "write err: %v", err)
						}
					case d := <-done:
						timer.Stop()
						if d.Code == net.MSG_STATE_OK {
							flagMap.Store(msgFlag, d.Data)
							var send = encodeMsgWithData(d.Code, msgType, msgFlag, d.Jackpot, d.Data)
							if err := c.WriteMessage(mt, send); err != nil {
								logger.Error("ws", "write err: %v", err)
							}
						} else {
							var send = encodeMsg(d.Code, msgType, msgFlag)
							if err := c.WriteMessage(mt, send); err != nil {
								logger.Error("ws", "write err: %v", err)
							}
						}
					}
				} else {
					// 无超时处理，直接等待结果
					d := <-done
					if d.Code == net.MSG_STATE_OK {
						flagMap.Store(msgFlag, d.Data)
						var send = encodeMsgWithData(d.Code, msgType, msgFlag, d.Jackpot, d.Data)
						if err := c.WriteMessage(mt, send); err != nil {
							logger.Error("ws", "write err: %v", err)
						}
					} else {
						var send = encodeMsg(d.Code, msgType, msgFlag)
						if err := c.WriteMessage(mt, send); err != nil {
							logger.Error("ws", "write err: %v", err)
						}
					}
				}
			}
		}
	}))

	app.Get("/stats/global/:cost", func(c *fiber.Ctx) error {
		cost := c.Params("cost")
		costInt, err := strconv.ParseInt(cost, 10, 64)

		if err != nil {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid cost",
			})
		}

		if costInt != net.COST_TYPE_100 && costInt != net.COST_TYPE_1000 && costInt != net.COST_TYPE_10000 {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid cost",
			})
		}

		var global = users.GetGlobalStats(costInt)
		return c.JSON(global)
	})

	app.Post("/stats/global/:cost/:key", func(c *fiber.Ctx) error {
		cost := c.Params("cost")
		costInt, err := strconv.ParseInt(cost, 10, 64)
		if err != nil {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid cost",
			})
		}

		if costInt != net.COST_TYPE_100 && costInt != net.COST_TYPE_1000 && costInt != net.COST_TYPE_10000 {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid cost",
			})
		}

		key := c.Params("key")
		var body struct {
			Value string `json:"value"`
		}
		if err := c.BodyParser(&body); err != nil {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid request body",
			})
		}

		// 使用反射检查并更新属性
		var global = users.GetGlobalStats(costInt)
		v := reflect.ValueOf(&global).Elem()
		field := v.Elem().FieldByName(key)
		if !field.IsValid() {
			return c.JSON(fiber.Map{
				"code":    2,
				"message": "Invalid key",
			})
		}

		// 根据字段类型进行赋值
		switch field.Kind() {
		case reflect.Float64:
			if value, err := strconv.ParseFloat(body.Value, 64); err == nil {
				field.SetFloat(value)
			} else {
				return c.JSON(fiber.Map{
					"code":    2,
					"message": "Invalid float value",
				})
			}
		case reflect.Int64:
			if value, err := strconv.ParseInt(body.Value, 10, 64); err == nil {
				field.SetInt(value)
			} else {
				return c.JSON(fiber.Map{
					"code":    2,
					"message": "Invalid int value",
				})
			}
		case reflect.String:
			field.SetString(body.Value)
		default:
			return c.JSON(fiber.Map{
				"code":    2,
				"message": "Unsupported field type",
			})
		}

		users.UpdateGobalStats(global)

		return c.JSON(fiber.Map{
			"code":    0,
			"message": "Updated successfully",
		})
	})

	app.Post("/stats/cleanup", func(c *fiber.Ctx) error {
		totalDeleted, err := users.Cleanup()
		if err != nil {
			logger.Error("redis-cleanup", "scan error: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code":    500,
				"message": "清理失败",
			})
		}

		logger.Info("redis-cleanup", "成功删除 %d 个key", totalDeleted)
		return c.JSON(fiber.Map{
			"code":    0,
			"message": fmt.Sprintf("清理完成，共删除 %d 个key", totalDeleted),
		})
	})

	// API模块信息端点 - 支持管理员模式
	app.Get("/api/modules", func(c *fiber.Ctx) error {
		// 检查是否为管理员模式
		adminMode := c.Query("admin") == "true"
		detailed := c.Query("detailed") == "true"

		modules := apiManager.GetModuleInfo()
		supportedTypes := apiManager.GetSupportedMessageTypes()

		// 基础响应数据
		responseData := fiber.Map{
			"modules":               modules,
			"supportedMessageTypes": supportedTypes,
			"totalModules":          len(modules),
		}

		// 管理员模式下添加额外信息
		if adminMode {
			// 添加系统信息
			responseData["systemInfo"] = fiber.Map{
				"version":      env.Version,
				"platform":     env.PlatId,
				"architecture": env.Arch,
				"debugMode":    env.Debug,
				"uptime":       time.Since(startTime).String(),
				"startTime":    startTime.Format("2006-01-02 15:04:05"),
			}

			// 添加配置信息
			responseData["configuration"] = fiber.Map{
				"port":          cfg.Section("base").Key("port").String(),
				"timerInterval": cfg.Section("base").Key("timer").String(),
				"logLevel":      cfg.Section("log").Key("lv").String(),
				"redisAddr":     cfg.Section("redis").Key("addr").String(),
			}

			// 添加模块详细状态
			if detailed {
				moduleDetails := make([]fiber.Map, 0, len(modules))
				for _, module := range modules {
					detail := fiber.Map{
						"name":        module.Name,
						"version":     module.Version,
						"messageType": module.MessageType,
						"status":      "active", // 所有注册的模块都是活跃的
						"initialized": true,     // 已初始化的模块
					}

					// 检查消息类型是否被支持
					if apiManager.IsMessageTypeSupported(module.MessageType) {
						detail["processorStatus"] = "registered"
					} else {
						detail["processorStatus"] = "not_registered"
					}

					moduleDetails = append(moduleDetails, detail)
				}
				responseData["moduleDetails"] = moduleDetails
			}

			// 添加运行时统计
			responseData["runtime"] = fiber.Map{
				"activeConnections": 0, // 可以从WebSocket连接池获取
				"processedMessages": 0, // 可以添加计数器
				"errorCount":        0, // 可以添加错误计数器
			}
		}

		return c.JSON(fiber.Map{
			"code":    0,
			"message": "success",
			"data":    responseData,
		})
	})

	logger.Warn("main", "arch:%s, debug:%v", env.Arch, env.Debug)

	port := cfg.Section("base").Key("port").String()
	app.Listen(":" + port)
}
