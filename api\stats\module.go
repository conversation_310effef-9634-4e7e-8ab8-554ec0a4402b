package stats

import (
	"fmt"
	"jackpot/api/core"
	"jackpot/filelog"
	"jackpot/net"
	"jackpot/users"
)

// Request 请求体
type Request struct {
	Mod    byte   `json:"mod"`
	Map    int64  `json:"map"`
	Cost   int64  `json:"cost"`
	Retn   int64  `json:"retn"`
	UserId string `json:"uid,omitempty"`
	Flag   int64  `json:"flag,omitempty"`
}

// Response 响应数据
type Response struct {
	Request
}

// Handler 统计处理器
type Handler struct {
	logger core.Logger
}

// NewHandler 创建统计处理器
func NewHandler() *Handler {
	return &Handler{}
}

// Init 初始化处理器
func (h *Handler) Init(logger *filelog.FileLogger, config any) error {
	h.logger = core.NewLoggerAdapter(logger)
	return nil
}

// Validate 验证请求
func (h *Handler) Validate(req Request) error {
	// 基本验证，stats模块比较宽松
	return nil
}

// Handle 处理请求
func (h *Handler) Handle(req Request) (int64, net.Response, error) {
	if req.Map != net.COST_TYPE_100 && req.Map != net.COST_TYPE_1000 && req.Map != net.COST_TYPE_10000 {
		req.Map = net.COST_TYPE_100 // 默认100
	}

	global := users.GetGlobalStats(req.Map)

	// 检查MOD参数
	switch req.Mod {
	case net.MSG_TYPE_OPEN_COMMON:
		global.TotalCost += req.Cost
		global.TotalJackpot += req.Cost
		h.logger.Debug(fmt.Sprintf("%d", req.Flag), "[%d] jackpot change global cost:%d return:%d and total cost:%d return:%d jackpot:%d", req.Mod, req.Cost, req.Retn, global.TotalCost, global.TotalReturn, global.TotalJackpot)
		users.UpdateGobalStats(global)
	case net.MSG_TYPE_OPEN_NEIDAN:
		var global = users.GetGlobalStats(req.Map)
		global.TotalJackpot += req.Retn
		h.logger.Debug(fmt.Sprintf("%d", req.Flag), "[%d] jackpot change global cost:%d return:%d and total cost:%d return:%d jackpot:%d", req.Mod, req.Cost, req.Retn, global.TotalCost, global.TotalReturn, global.TotalJackpot)
		users.UpdateGobalStats(global)
	case net.MSG_TYPE_OPEN_BOSS:
	case net.MSG_TYPE_OPEN_DAILY:
	case net.MSG_TYPE_OPEN_NEZHA:
	case net.MSG_TYPE_OPEN_ITEMS:
	case net.MSG_TYPE_OPEN_REROLL:
		var global = users.GetGlobalStats(req.Map)
		global.TotalReturn += req.Retn
		global.TotalJackpot -= req.Retn
		h.logger.Debug(fmt.Sprintf("%d", req.Flag), "[%d] jackpot change global cost:%d return:%d and total cost:%d return:%d jackpot:%d", req.Mod, req.Cost, req.Retn, global.TotalCost, global.TotalReturn, global.TotalJackpot)
		users.UpdateGobalStats(global)
	default:
		return 0, net.Response{Code: 2, Msg: "Invalid mod parameter", Data: nil}, nil
	}

	response := net.Response{
		Code: 0,
		Msg:  "",
		Data: &Response{
			Request: req,
		},
	}

	return global.TotalJackpot, response, nil
}

// NewModule 创建统计模块
func NewModule() core.APIModule {
	handler := NewHandler()

	return core.NewModuleBuilder[Request, net.Response]("stats", "v1", net.MSG_TYPE_OPEN_PUT).
		WithHandler(handler).
		Build()
}
